{"dns": {"servers": [{"tag": "remote", "address": "tcp://*******", "strategy": "prefer_ipv4", "detour": "proxy"}, {"tag": "local", "address": "*********", "strategy": "prefer_ipv4", "detour": "direct"}], "rules": [{"rule_set": ["geosite-cn"], "server": "local"}], "final": "remote"}, "inbounds": [{"type": "tun", "inet4_address": "**********/30", "auto_route": true, "strict_route": true, "sniff": true}], "outbounds": [{"type": "selector", "tag": "proxy", "outbounds": ["auto", "direct"], "default": "auto"}, {"type": "urltest", "tag": "auto", "outbounds": ["v2ray-out"], "url": "http://www.gstatic.com/generate_204", "interval": "1m"}, {"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}, {"type": "http", "tag": "v2ray-out", "server": "127.0.0.1", "server_port": 10809}], "route": {"rules": [{"rule_set": ["geosite-cn"], "outbound": "direct"}, {"rule_set": ["geoip-cn"], "outbound": "direct"}], "final": "proxy", "auto_detect_interface": true}}