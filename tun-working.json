{"dns": {"servers": [{"tag": "remote", "address": "tcp://*******", "strategy": "prefer_ipv4", "detour": "proxy"}, {"tag": "local", "address": "*********", "strategy": "prefer_ipv4", "detour": "direct"}], "rules": [{"rule_set": ["geosite-cn"], "server": "local"}], "final": "remote"}, "inbounds": [{"type": "tun", "tag": "tun-in", "interface_name": "tun0", "inet4_address": "**********/30", "auto_route": true, "strict_route": true, "sniff": true}], "outbounds": [{"type": "selector", "tag": "proxy", "outbounds": ["v2ray-local"], "default": "v2ray-local"}, {"type": "direct", "tag": "direct"}, {"type": "vmess", "tag": "v2ray-local", "server": "127.0.0.1", "server_port": 10808, "uuid": "auto-generated", "security": "auto"}], "route": {"rules": [{"rule_set": ["geosite-cn"], "outbound": "direct"}, {"rule_set": ["geoip-cn"], "outbound": "direct"}], "final": "proxy", "auto_detect_interface": true}}