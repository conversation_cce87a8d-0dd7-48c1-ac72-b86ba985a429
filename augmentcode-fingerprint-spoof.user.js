// ==UserScript==
// @name         AugmentCode 设备指纹伪造器
// @namespace    http://tampermonkey.net/
// @version      2.0.0
// @description  专门针对 AugmentCode 的设备指纹伪造，绕过注册检测
// <AUTHOR>
// @match        *://*.augmentcode.com/*
// @match        *://augmentcode.com/*
// @match        *://login.augmentcode.com/*
// @match        *://auth.augmentcode.com/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function () {
  "use strict";

  // 配置选项 - 模拟美国用户环境
  const CONFIG = {
    fakeUserAgent:
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    fakePlatform: "Win32",
    fakeLanguage: "en-US",
    fakeLanguages: ["en-US", "en"],
    fakeTimezone: "America/New_York",
    fakeTimezoneOffset: 300, // EST offset in minutes
    fakeScreen: {
      width: 1920,
      height: 1080,
      availWidth: 1920,
      availHeight: 1040,
      colorDepth: 24,
      pixelDepth: 24,
    },
    enableDebugLog: true,
    blockAnalytics: true,
    spoofPlugins: true,
    spoofFonts: true,
    spoofCSS: true,
    spoofBattery: true,
    spoofConnection: true,
    randomizeFingerprint: true,
  };

  function log(message) {
    if (CONFIG.enableDebugLog) {
      console.log(`[AugmentCode Spoof] ${message}`);
    }
  }

  // 1. Navigator 对象伪造
  function spoofNavigator() {
    // User Agent
    Object.defineProperty(navigator, "userAgent", {
      get: () => CONFIG.fakeUserAgent,
      configurable: true,
    });

    // Platform
    Object.defineProperty(navigator, "platform", {
      get: () => CONFIG.fakePlatform,
      configurable: true,
    });

    // Language
    Object.defineProperty(navigator, "language", {
      get: () => CONFIG.fakeLanguage,
      configurable: true,
    });

    Object.defineProperty(navigator, "languages", {
      get: () => CONFIG.fakeLanguages,
      configurable: true,
    });

    // Hardware concurrency (CPU cores)
    Object.defineProperty(navigator, "hardwareConcurrency", {
      get: () => 8,
      configurable: true,
    });

    // Memory (if available)
    if ("deviceMemory" in navigator) {
      Object.defineProperty(navigator, "deviceMemory", {
        get: () => 8,
        configurable: true,
      });
    }

    log("Navigator spoofed");
  }

  // 2. Screen 对象伪造
  function spoofScreen() {
    Object.defineProperty(screen, "width", {
      get: () => CONFIG.fakeScreen.width,
      configurable: true,
    });

    Object.defineProperty(screen, "height", {
      get: () => CONFIG.fakeScreen.height,
      configurable: true,
    });

    Object.defineProperty(screen, "availWidth", {
      get: () => CONFIG.fakeScreen.availWidth,
      configurable: true,
    });

    Object.defineProperty(screen, "availHeight", {
      get: () => CONFIG.fakeScreen.availHeight,
      configurable: true,
    });

    Object.defineProperty(screen, "colorDepth", {
      get: () => CONFIG.fakeScreen.colorDepth,
      configurable: true,
    });

    Object.defineProperty(screen, "pixelDepth", {
      get: () => CONFIG.fakeScreen.pixelDepth,
      configurable: true,
    });

    log("Screen spoofed");
  }

  // 3. 时区伪造
  function spoofTimezone() {
    // 重写 Date 的 getTimezoneOffset 方法
    const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
    Date.prototype.getTimezoneOffset = function () {
      return CONFIG.fakeTimezoneOffset;
    };

    // 重写 Intl.DateTimeFormat
    const originalDateTimeFormat = Intl.DateTimeFormat;
    Intl.DateTimeFormat = function (...args) {
      const instance = new originalDateTimeFormat(...args);
      const originalResolvedOptions = instance.resolvedOptions;
      instance.resolvedOptions = function () {
        const options = originalResolvedOptions.call(this);
        options.timeZone = CONFIG.fakeTimezone;
        return options;
      };
      return instance;
    };

    log("Timezone spoofed");
  }

  // 4. WebRTC 禁用
  function disableWebRTC() {
    // 禁用 getUserMedia
    if (navigator.mediaDevices) {
      navigator.mediaDevices.getUserMedia = undefined;
    }
    if (navigator.getUserMedia) {
      navigator.getUserMedia = undefined;
    }

    // 禁用 RTCPeerConnection
    window.RTCPeerConnection = undefined;
    window.webkitRTCPeerConnection = undefined;
    window.mozRTCPeerConnection = undefined;

    log("WebRTC disabled");
  }

  // 5. Canvas 指纹干扰
  function spoofCanvas() {
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    const originalGetImageData =
      CanvasRenderingContext2D.prototype.getImageData;

    HTMLCanvasElement.prototype.toDataURL = function (...args) {
      // 添加微小的随机噪声
      const ctx = this.getContext("2d");
      if (ctx) {
        const imageData = ctx.getImageData(0, 0, 1, 1);
        imageData.data[0] =
          imageData.data[0] + Math.floor(Math.random() * 3) - 1;
        ctx.putImageData(imageData, 0, 0);
      }
      return originalToDataURL.apply(this, args);
    };

    log("Canvas spoofed");
  }

  // 6. WebGL 指纹干扰
  function spoofWebGL() {
    const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
    WebGLRenderingContext.prototype.getParameter = function (parameter) {
      // 伪造常见的 WebGL 参数
      if (parameter === this.RENDERER) {
        return "ANGLE (Intel, Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0, D3D11)";
      }
      if (parameter === this.VENDOR) {
        return "Google Inc. (Intel)";
      }
      return originalGetParameter.apply(this, arguments);
    };

    log("WebGL spoofed");
  }

  // 7. 阻止分析工具
  function blockAnalytics() {
    if (!CONFIG.blockAnalytics) return;

    // 阻止 PostHog
    window.posthog = new Proxy(
      {},
      {
        get: () => () => {},
        set: () => true,
      }
    );

    // 阻止 Google Analytics
    window.gtag = () => {};
    window.ga = () => {};

    // 阻止 Segment
    window.analytics = new Proxy(
      {},
      {
        get: () => () => {},
        set: () => true,
      }
    );

    log("Analytics blocked");
  }

  // 8. 添加人类行为模拟
  function simulateHumanBehavior() {
    // 随机鼠标移动
    let mouseX = 0,
      mouseY = 0;
    setInterval(() => {
      mouseX += (Math.random() - 0.5) * 10;
      mouseY += (Math.random() - 0.5) * 10;
      mouseX = Math.max(0, Math.min(window.innerWidth, mouseX));
      mouseY = Math.max(0, Math.min(window.innerHeight, mouseY));

      document.dispatchEvent(
        new MouseEvent("mousemove", {
          clientX: mouseX,
          clientY: mouseY,
        })
      );
    }, 2000 + Math.random() * 3000);

    log("Human behavior simulation started");
  }

  // 9. 插件伪造
  function spoofPlugins() {
    if (!CONFIG.spoofPlugins) return;

    // 伪造常见插件
    const fakePlugins = [
      { name: "Chrome PDF Plugin", filename: "internal-pdf-viewer" },
      {
        name: "Chrome PDF Viewer",
        filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
      },
      { name: "Native Client", filename: "internal-nacl-plugin" },
    ];

    Object.defineProperty(navigator, "plugins", {
      get: () => fakePlugins,
      configurable: true,
    });

    log("Plugins spoofed");
  }

  // 10. 字体检测干扰
  function spoofFonts() {
    if (!CONFIG.spoofFonts) return;

    // 干扰字体检测
    const originalOffsetWidth = Object.getOwnPropertyDescriptor(
      HTMLElement.prototype,
      "offsetWidth"
    );
    const originalOffsetHeight = Object.getOwnPropertyDescriptor(
      HTMLElement.prototype,
      "offsetHeight"
    );

    Object.defineProperty(HTMLElement.prototype, "offsetWidth", {
      get: function () {
        const width = originalOffsetWidth.get.call(this);
        return width + (Math.random() - 0.5) * 0.1;
      },
    });

    Object.defineProperty(HTMLElement.prototype, "offsetHeight", {
      get: function () {
        const height = originalOffsetHeight.get.call(this);
        return height + (Math.random() - 0.5) * 0.1;
      },
    });

    log("Font detection spoofed");
  }

  // 11. CSS 媒体查询干扰
  function spoofCSS() {
    if (!CONFIG.spoofCSS) return;

    const originalMatchMedia = window.matchMedia;
    window.matchMedia = function (query) {
      const result = originalMatchMedia.call(this, query);
      // 对某些敏感查询返回假结果
      if (query.includes("prefers-color-scheme")) {
        return { matches: false, media: query };
      }
      return result;
    };

    log("CSS media queries spoofed");
  }

  // 12. 电池 API 伪造
  function spoofBattery() {
    if (!CONFIG.spoofBattery) return;

    if ("getBattery" in navigator) {
      navigator.getBattery = () =>
        Promise.resolve({
          charging: true,
          chargingTime: 0,
          dischargingTime: Infinity,
          level: 1.0,
        });
    }

    log("Battery API spoofed");
  }

  // 13. 网络连接信息伪造
  function spoofConnection() {
    if (!CONFIG.spoofConnection) return;

    if ("connection" in navigator) {
      Object.defineProperty(navigator, "connection", {
        get: () => ({
          effectiveType: "4g",
          downlink: 10,
          rtt: 50,
          saveData: false,
        }),
        configurable: true,
      });
    }

    log("Connection info spoofed");
  }

  // 14. 高级 WebDriver 检测绕过
  function spoofWebDriver() {
    // 移除 webdriver 属性
    Object.defineProperty(navigator, "webdriver", {
      get: () => undefined,
      configurable: true,
    });

    // 伪造 chrome 对象
    if (!window.chrome) {
      window.chrome = {
        runtime: {},
        loadTimes: function () {},
        csi: function () {},
        app: {},
      };
    }

    // 移除自动化相关属性
    delete window.callPhantom;
    delete window._phantom;
    delete window.__nightmare;
    delete window._selenium;
    delete window.webdriver;
    delete window.domAutomation;
    delete window.domAutomationController;

    log("WebDriver detection bypassed");
  }

  // 15. 权限 API 伪造
  function spoofPermissions() {
    if ("permissions" in navigator) {
      const originalQuery = navigator.permissions.query;
      navigator.permissions.query = function (permissionDesc) {
        return Promise.resolve({ state: "prompt" });
      };
    }

    log("Permissions API spoofed");
  }

  // 16. 增强的行为模拟
  function enhancedHumanBehavior() {
    // 模拟滚动行为
    let scrollPosition = 0;
    setInterval(() => {
      const maxScroll = document.body.scrollHeight - window.innerHeight;
      if (maxScroll > 0) {
        scrollPosition += (Math.random() - 0.5) * 100;
        scrollPosition = Math.max(0, Math.min(maxScroll, scrollPosition));
        window.scrollTo(0, scrollPosition);
      }
    }, 5000 + Math.random() * 10000);

    // 模拟键盘事件
    setInterval(() => {
      const keys = ["Tab", "Enter", "Escape"];
      const randomKey = keys[Math.floor(Math.random() * keys.length)];
      document.dispatchEvent(new KeyboardEvent("keydown", { key: randomKey }));
    }, 10000 + Math.random() * 20000);

    // 模拟窗口焦点变化
    setInterval(() => {
      window.dispatchEvent(new Event("blur"));
      setTimeout(() => {
        window.dispatchEvent(new Event("focus"));
      }, 1000 + Math.random() * 2000);
    }, 30000 + Math.random() * 60000);

    log("Enhanced human behavior simulation started");
  }

  // 17. 反 Cloudflare 检测
  function antiCloudflareDetection() {
    // 干扰 Cloudflare 的 PAT 检测
    const originalFetch = window.fetch;
    window.fetch = function (...args) {
      const url = args[0];
      if (
        typeof url === "string" &&
        url.includes("challenges.cloudflare.com")
      ) {
        log("Cloudflare challenge detected, adding delays...");
        // 添加随机延迟模拟人类行为
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve(originalFetch.apply(this, args));
          }, 1000 + Math.random() * 2000);
        });
      }
      return originalFetch.apply(this, args);
    };

    // 干扰 Turnstile 验证码
    const originalCreateElement = document.createElement;
    document.createElement = function (tagName) {
      const element = originalCreateElement.call(this, tagName);
      if (
        tagName.toLowerCase() === "iframe" &&
        element.src &&
        element.src.includes("turnstile")
      ) {
        log("Turnstile iframe detected");
        // 添加延迟加载
        setTimeout(() => {
          element.style.visibility = "visible";
        }, 500 + Math.random() * 1000);
      }
      return element;
    };

    // 模拟真实的鼠标轨迹
    let mouseTrail = [];
    document.addEventListener("mousemove", (e) => {
      mouseTrail.push({ x: e.clientX, y: e.clientY, time: Date.now() });
      if (mouseTrail.length > 50) mouseTrail.shift();
    });

    // 重写 performance.now() 添加微小随机性
    const originalPerformanceNow = performance.now;
    performance.now = function () {
      return originalPerformanceNow.call(this) + (Math.random() - 0.5) * 0.1;
    };

    log("Anti-Cloudflare detection activated");
  }

  // 18. 深度浏览器环境伪造
  function deepBrowserSpoof() {
    // 伪造 Chrome 特有的对象
    if (!window.chrome) {
      window.chrome = {
        runtime: {
          onConnect: { addListener: () => {} },
          onMessage: { addListener: () => {} },
        },
        loadTimes: () => ({
          requestTime: Date.now() / 1000 - Math.random() * 10,
          startLoadTime: Date.now() / 1000 - Math.random() * 5,
          commitLoadTime: Date.now() / 1000 - Math.random() * 3,
          finishDocumentLoadTime: Date.now() / 1000 - Math.random() * 2,
          finishLoadTime: Date.now() / 1000 - Math.random(),
          firstPaintTime: Date.now() / 1000 - Math.random() * 4,
          firstPaintAfterLoadTime: 0,
          navigationType: "Other",
        }),
        csi: () => ({
          pageT: Math.random() * 1000 + 500,
          startE: Date.now() - Math.random() * 10000,
          tran: 15,
        }),
      };
    }

    // 伪造 window.external
    if (!window.external) {
      window.external = {
        AddSearchProvider: () => {},
        IsSearchProviderInstalled: () => 0,
      };
    }

    // 添加 Chrome 特有的 CSS 属性支持检测
    const style = document.createElement("div").style;
    if (!("webkitAppearance" in style)) {
      Object.defineProperty(style, "webkitAppearance", {
        get: () => "",
        set: () => {},
        configurable: true,
      });
    }

    log("Deep browser environment spoofed");
  }

  // 主函数 - 立即执行所有伪造
  function initSpoof() {
    log("Starting fingerprint spoofing...");

    spoofNavigator();
    spoofScreen();
    spoofTimezone();
    disableWebRTC();
    spoofCanvas();
    spoofWebGL();
    blockAnalytics();
    spoofPlugins();
    spoofFonts();
    spoofCSS();
    spoofBattery();
    spoofConnection();
    spoofWebDriver();
    spoofPermissions();
    antiCloudflareDetection();
    deepBrowserSpoof();

    // 页面加载后启动行为模拟
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () => {
        simulateHumanBehavior();
        enhancedHumanBehavior();
      });
    } else {
      simulateHumanBehavior();
      enhancedHumanBehavior();
    }

    log("Fingerprint spoofing completed!");
    log(`Fake User-Agent: ${navigator.userAgent}`);
    log(`Fake Language: ${navigator.language}`);
    log(`Fake Timezone Offset: ${new Date().getTimezoneOffset()}`);
  }

  // 立即执行
  initSpoof();
})();
