{"log": {"level": "warn", "output": "sing-box.log"}, "dns": {"servers": [{"tag": "google", "address": "https://*******/dns-query", "strategy": "prefer_ipv4", "detour": "proxy"}, {"tag": "cloudflare", "address": "https://*******/dns-query", "strategy": "prefer_ipv4", "detour": "proxy"}, {"tag": "local", "address": "*********", "strategy": "prefer_ipv4", "detour": "direct"}, {"tag": "block", "address": "rcode://success"}], "rules": [{"domain_suffix": ["gmail.com", "google.com", "googleapis.com", "googleusercontent.com", "gstatic.com"], "server": "google"}, {"domain_suffix": ["augmentcode.com", "auth0.com", "cloudflare.com"], "server": "cloudflare"}, {"rule_set": ["geosite-cn", "geosite-geolocation-cn"], "server": "local"}], "final": "google"}, "inbounds": [{"type": "tun", "tag": "tun-in", "interface_name": "tun0", "inet4_address": "**********/30", "auto_route": true, "strict_route": true, "sniff": true, "sniff_override_destination": false}], "outbounds": [{"type": "selector", "tag": "proxy", "outbounds": ["auto", "direct"], "default": "auto"}, {"type": "urltest", "tag": "auto", "outbounds": ["v2ray"], "url": "http://www.gstatic.com/generate_204", "interval": "1m", "tolerance": 50}, {"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}, {"type": "shadowsocks", "tag": "proxy-server", "server": "**********", "server_port": 443, "method": "aes-256-gcm", "password": "your-password-here"}], "route": {"rules": [{"domain_suffix": ["gmail.com", "google.com", "googleapis.com", "googleusercontent.com", "gstatic.com", "augmentcode.com", "auth0.com"], "outbound": "proxy"}, {"rule_set": ["geosite-cn", "geosite-geolocation-cn"], "outbound": "direct"}, {"rule_set": ["geoip-cn"], "outbound": "direct"}], "final": "proxy", "auto_detect_interface": true}}